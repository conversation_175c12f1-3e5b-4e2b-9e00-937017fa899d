
from datetime import datetime
from typing import Sequence
from uuid import UUID
from pydantic import BaseModel, Field, field_validator


class PollenSubspecies(BaseModel):
    name: str 
    count: int


class PollenSpecies(BaseModel):
    count: int
    subspecies: Sequence[PollenSubspecies] | None

class CoordinatesModel(BaseModel):
    latitude: float 
    longitude: float

    @field_validator("latitude", "longitude")
    @classmethod
    def result_check(cls, value):
        return round(value, 7)

class Environment(BaseModel):
    id: UUID
    timestamp: datetime
    coordinates: CoordinatesModel

class Pollen(Environment):
    tree: PollenSpecies | None 
    weed: PollenSpecies | None 
    grass: PollenSpecies | None 
