
import datetime
from typing import Sequence
from uuid import UUID
from pydantic import BaseModel, Field, field_validator


@dataclass(frozen=True)
class PollenFields:
    # Subspecies
    SUBSPECIES_NAME: str = "name"
    SUBSPECIES_COUNT: str = "count"

    # Species
    SPECIES_COUNT: str = "count"
    SUBSPECIES: str = "subspecies"

    # Top-level fields
    TREE: str = "tree"
    GRASS: str = "grass"
    WEED: str = "weed"
    COORDINATES: str = "coordinates"
    METADATA: str = DocumentLabels.METADATA
    SYSTEM_PROPERTIES: str = DocumentLabels.SYSTEM_PROPERTIES


class PollenSubspecies(BaseModel):
    name: str = Field(alias=PollenFields.SUBSPECIES_NAME)
    count: int = Field(alias=PollenFields.SUBSPECIES_COUNT)


class PollenSpecies(BaseModel):
    count: int = Field(alias=PollenFields.SPECIES_COUNT)
    subspecies: Sequence[PollenSubspecies] | None = Field(alias=PollenFields.SUBSPECIES, default=None)

class CoordinatesModel(BaseDataModel):
    latitude: float 
    longitude: float

    @field_validator("latitude", "longitude")
    @classmethod
    def result_check(cls, value):
        return round(value, 7)

class Environment(TimestampModel, Document):
    id: UUID = Field(alias=DocumentLabels.ID)
    timestamp: datetime = Field(alias=DocumentLabels.TIMESTAMP)
    coordinates: CoordinatesModel = Field(alias=EnvironmentLabels.COORDINATES)

class Pollen(Environment):
    tree: PollenSpecies | None = Field(alias=PollenFields.TREE, default=None)
    weed: PollenSpecies | None = Field(alias=PollenFields.WEED, default=None)
    grass: PollenSpecies | None = Field(alias=PollenFields.GRASS, default=None)
