import datetime
from typing import Optional
from uuid import uuid4
import aiohttp
from models.pollen import Pollen, PollenSpecies, PollenSubspecies, CoordinatesModel


class OpenMeteoProvider:
    """OpenMeteo pollen data provider."""

    def __init__(self):
        """Initialize OpenMeteo provider (no API key required)."""
        self.base_url = "https://air-quality-api.open-meteo.com/v1"

    async def fetch_pollen(self, lat: float, lon: float) -> Optional[Pollen]:
        """Fetch pollen data for given coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None if data unavailable
        """
        url = f"{self.base_url}/air-quality"
        params = {
            "latitude": lat,
            "longitude": lon,
            "hourly": "grass_pollen,alder_pollen,birch_pollen,mugwort_pollen,olive_pollen,ragweed_pollen",
            "forecast_days": 1
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()

                    return self._parse_response(data, lat, lon)
            except Exception:
                return None

    def _parse_response(self, data: dict, lat: float, lon: float) -> Optional[Pollen]:
        """Parse OpenMeteo response into Pollen model.

        Args:
            data: OpenMeteo API response
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None
        """
        if not data or not data.get("hourly"):
            return None

        hourly_data = data["hourly"]

        # Get the most recent data point (last index)
        latest_idx = -1

        # Extract pollen counts
        grass_pollen = hourly_data.get("grass_pollen", [0])[latest_idx] or 0
        ragweed_pollen = hourly_data.get("ragweed_pollen", [0])[latest_idx] or 0
        mugwort_pollen = hourly_data.get("mugwort_pollen", [0])[latest_idx] or 0

        # Tree subspecies
        alder_pollen = hourly_data.get("alder_pollen", [0])[latest_idx] or 0
        birch_pollen = hourly_data.get("birch_pollen", [0])[latest_idx] or 0
        olive_pollen = hourly_data.get("olive_pollen", [0])[latest_idx] or 0

        # Calculate total weed pollen
        weed_pollen = ragweed_pollen + mugwort_pollen

        # Calculate total tree pollen from subspecies
        tree_pollen = alder_pollen + birch_pollen + olive_pollen

        # Create tree subspecies if available
        tree_subspecies = []
        if alder_pollen > 0:
            tree_subspecies.append(PollenSubspecies(name="alder", count=int(alder_pollen)))
        if birch_pollen > 0:
            tree_subspecies.append(PollenSubspecies(name="birch", count=int(birch_pollen)))
        if olive_pollen > 0:
            tree_subspecies.append(PollenSubspecies(name="olive", count=int(olive_pollen)))

        # Create weed subspecies if available
        weed_subspecies = []
        if ragweed_pollen > 0:
            weed_subspecies.append(PollenSubspecies(name="ragweed", count=int(ragweed_pollen)))
        if mugwort_pollen > 0:
            weed_subspecies.append(PollenSubspecies(name="mugwort", count=int(mugwort_pollen)))

        # Create pollen species objects
        tree_species = PollenSpecies(
            count=int(tree_pollen),
            subspecies=tree_subspecies if tree_subspecies else None
        ) if tree_pollen > 0 else None

        grass_species = PollenSpecies(
            count=int(grass_pollen),
            subspecies=None
        ) if grass_pollen > 0 else None

        weed_species = PollenSpecies(
            count=int(weed_pollen),
            subspecies=weed_subspecies if weed_subspecies else None
        ) if weed_pollen > 0 else None

        return Pollen(
            id=uuid4(),
            timestamp=datetime.datetime.now(datetime.timezone.utc),
            coordinates=CoordinatesModel(latitude=lat, longitude=lon),
            tree=tree_species,
            grass=grass_species,
            weed=weed_species
        )