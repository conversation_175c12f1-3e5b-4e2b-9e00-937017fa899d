import datetime
from typing import Optional
from uuid import uuid4
import aiohttp
from models.pollen import Pollen, PollenSpecies, PollenSubspecies, CoordinatesModel


class AmbeeProvider:
    """Ambee pollen data provider."""

    def __init__(self, api_key: str):
        """Initialize with Ambee API key.

        Args:
            api_key: Ambee API key
        """
        self.api_key = api_key
        self.base_url = "https://api.ambee.com/v1"

    async def fetch_pollen(self, lat: float, lon: float) -> Optional[Pollen]:
        """Fetch pollen data for given coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None if data unavailable
        """
        url = f"{self.base_url}/latest/pollen"
        params = {
            "lat": lat,
            "lng": lon
        }
        headers = {
            "x-api-key": self.api_key,
            "Content-type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, headers=headers, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()

                    return self._parse_response(data, lat, lon)
            except Exception:
                return None

    def _parse_response(self, data: dict, lat: float, lon: float) -> Optional[Pollen]:
        """Parse Ambee response into Pollen model.

        Args:
            data: Ambee API response
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None
        """
        if not data or not data.get("data"):
            return None

        pollen_data = data["data"][0]  # Get latest data point
        count_data = pollen_data.get("Count", {})
        species_data = pollen_data.get("Species", {})

        # Extract main counts
        tree_count = count_data.get("tree_pollen", 0)
        grass_count = count_data.get("grass_pollen", 0)
        weed_count = count_data.get("weed_pollen", 0)

        # Create subspecies for tree if available
        tree_subspecies = None
        if species_data.get("Tree"):
            tree_subspecies = []
            for species_name, species_count in species_data["Tree"].items():
                if species_count > 0:
                    tree_subspecies.append(PollenSubspecies(name=species_name, count=species_count))

        # Create subspecies for grass if available
        grass_subspecies = None
        if species_data.get("Grass"):
            grass_subspecies = []
            for species_name, species_count in species_data["Grass"].items():
                if species_count > 0:
                    grass_subspecies.append(PollenSubspecies(name=species_name, count=species_count))

        # Create subspecies for weed if available
        weed_subspecies = None
        if species_data.get("Weed"):
            weed_subspecies = []
            for species_name, species_count in species_data["Weed"].items():
                if species_count > 0:
                    weed_subspecies.append(PollenSubspecies(name=species_name, count=species_count))

        # Create pollen species objects
        tree_species = PollenSpecies(count=tree_count, subspecies=tree_subspecies) if tree_count > 0 else None
        grass_species = PollenSpecies(count=grass_count, subspecies=grass_subspecies) if grass_count > 0 else None
        weed_species = PollenSpecies(count=weed_count, subspecies=weed_subspecies) if weed_count > 0 else None

        return Pollen(
            id=uuid4(),
            timestamp=datetime.datetime.now(datetime.timezone.utc),
            coordinates=CoordinatesModel(latitude=lat, longitude=lon),
            tree=tree_species,
            grass=grass_species,
            weed=weed_species
        )