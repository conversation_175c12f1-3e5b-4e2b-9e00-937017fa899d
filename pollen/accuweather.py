import datetime
from typing import Optional
from uuid import uuid4
import aiohttp
from models.pollen import Pollen, PollenSpecies, CoordinatesModel


class AccuWeatherProvider:
    """AccuWeather pollen data provider."""

    def __init__(self, api_key: str):
        """Initialize with AccuWeather API key.

        Args:
            api_key: AccuWeather API key
        """
        self.api_key = api_key
        self.base_url = "http://dataservice.accuweather.com"

    async def _get_location_key(self, lat: float, lon: float) -> Optional[str]:
        """Get AccuWeather location key for coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Location key string or None if not found
        """
        url = f"{self.base_url}/locations/v1/cities/geoposition/search"
        params = {
            "apikey": self.api_key,
            "q": f"{lat},{lon}",
            "details": "false"
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()
                    return data.get("Key")
            except Exception:
                return None

    async def fetch_pollen(self, lat: float, lon: float) -> Optional[Pollen]:
        """Fetch pollen data for given coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None if data unavailable
        """
        # First get location key
        location_key = await self._get_location_key(lat, lon)
        if not location_key:
            return None

        # Fetch pollen indices
        url = f"{self.base_url}/indices/v1/daily/1day/{location_key}"
        params = {
            "apikey": self.api_key,
            "details": "true"
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()

                    return self._parse_response(data, lat, lon)
            except Exception:
                return None

    def _parse_response(self, data: list, lat: float, lon: float) -> Optional[Pollen]:
        """Parse AccuWeather response into Pollen model.

        Args:
            data: AccuWeather API response
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None
        """
        tree_count = 0
        grass_count = 0
        weed_count = 0

        # Parse pollen data from indices
        for item in data:
            name = item.get("Name", "").lower()
            value = item.get("CategoryValue", item.get("Value", 0))

            if "tree" in name and "pollen" in name:
                tree_count = value
            elif "grass" in name and "pollen" in name:
                grass_count = value
            elif any(term in name for term in ["ragweed", "weed"]) and "pollen" in name:
                weed_count = value

        # Create pollen species objects
        tree_species = PollenSpecies(count=tree_count, subspecies=None) if tree_count > 0 else None
        grass_species = PollenSpecies(count=grass_count, subspecies=None) if grass_count > 0 else None
        weed_species = PollenSpecies(count=weed_count, subspecies=None) if weed_count > 0 else None

        return Pollen(
            id=uuid4(),
            timestamp=datetime.datetime.now(datetime.timezone.utc),
            coordinates=CoordinatesModel(latitude=lat, longitude=lon),
            tree=tree_species,
            grass=grass_species,
            weed=weed_species
        )