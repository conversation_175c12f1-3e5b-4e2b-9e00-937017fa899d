import streamlit as st
import pandas as pd
import requests
import plotly.express as px
from datetime import datetime, timedelta
import random

# --- Configuration ---
# List of locations to monitor
LOCATIONS = {
    "Olomouc, Czechia": {"lat": 49.5937, "lon": 17.2518, "accuweather_key": "OLOMOUC_AW_KEY"},
    "Berlin, Germany": {"lat": 52.5200, "lon": 13.4050, "accuweather_key": "BERLIN_AW_KEY"},
    "Paris, France": {"lat": 48.8566, "lon": 2.3522, "accuweather_key": "PARIS_AW_KEY"}
}

# API Keys (set these in .streamlit/secrets.toml or Streamlit Cloud secrets)
AMBEE_API_KEY = "ERs8qeHdFB2revJ6oYPak8OEDGtFlcWw5JBV6tnR"
ACCUWEATHER_API_KEY = "********************************"
GOOGLE_POLLEN_API_KEY = "AIzaSyC24uT3V0QRc8TFGhRHiugp7S4icvHHjRo"


# --- API Fetching Functions ---

@st.cache_data(ttl=3600) # Cache for 1 hour
def fetch_ambee_pollen(location_name, lat, lon, api_key):
    if not api_key:
        return {"Tree": random.randint(3, 8), "Grass": random.randint(2, 7), "Weed": random.randint(1, 6), "Overall": random.randint(4, 9)}
    try:
        url = f"https://api.ambee.com/v1/latest/pollen?lat={lat}&lng={lon}"
        headers = {"x-api-key": api_key, "Content-type": "application/json"}
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        # --- REPLACE THIS DUMMY PARSING WITH ACTUAL AMBEE RESPONSE PARSING ---
        if data and data.get("data") and data["data"]:
            pollen_info = data["data"][0] # Assuming latest data is the first item
            tree = pollen_info.get("Count", {}).get("tree", 0)
            grass = pollen_info.get("Count", {}).get("grass", 0)
            weed = pollen_info.get("Count", {}).get("weed", 0)
            
            # Ambee often gives raw counts, convert to 0-10 index if needed
            # This is a simple linear scaling, adjust based on Ambee's docs
            # Assuming typical max counts are 1000 for trees, 500 for grass, 300 for weed
            pollen_levels = {
                "Tree": min(10, round(tree / 100)),
                "Grass": min(10, round(grass / 50)),
                "Weed": min(10, round(weed / 30)),
            }
            pollen_levels["Overall"] = min(10, round((pollen_levels["Tree"] + pollen_levels["Grass"] + pollen_levels["Weed"]) / 3))
            return pollen_levels
        return None
    except Exception as e:
        st.error(f"Ambee error for {location_name}: {e}")
        return None

@st.cache_data(ttl=3600)
def fetch_openmeteo_pollen(location_name, lat, lon):
    try:
        # OpenMeteo includes 'tree_pollen' (alder, birch, olive) and 'grass_pollen', 'ragweed_pollen', 'mugwort_pollen'
        url = (f"https://air-quality-api.open-meteo.com/v1/air-quality?"
               f"latitude={lat}&longitude={lon}&hourly=grass_pollen,tree_pollen,ragweed_pollen,mugwort_pollen"
               f"&forecast_days=1&current=true&timezone=auto")
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data and data.get("hourly") and data["hourly"].get("time"):
            hourly_data = data["hourly"]
            
            # OpenMeteo provides raw values, you need to map them to an index (0-10)
            # These max_raw_values are assumptions; adjust based on OpenMeteo's scale
            max_raw_tree = 100
            max_raw_grass = 50
            max_raw_weed = 30
            
            latest_idx = -1 # Get the most recent hourly data point
            
            tree_pollen = hourly_data.get("tree_pollen", [0])[latest_idx]
            grass_pollen = hourly_data.get("grass_pollen", [0])[latest_idx]
            # Sum up weed types if available
            weed_pollen = hourly_data.get("ragweed_pollen", [0])[latest_idx] + hourly_data.get("mugwort_pollen", [0])[latest_idx]

            pollen_levels = {
                "Tree": min(10, round((tree_pollen / max_raw_tree) * 10)),
                "Grass": min(10, round((grass_pollen / max_raw_grass) * 10)),
                "Weed": min(10, round((weed_pollen / max_raw_weed) * 10)),
            }
            pollen_levels["Overall"] = min(10, round((pollen_levels["Tree"] + pollen_levels["Grass"] + pollen_levels["Weed"]) / 3))
            return pollen_levels
        return None
    except Exception as e:
        st.error(f"OpenMeteo error for {location_name}: {e}")
        return None

@st.cache_data(ttl=3600)
def fetch_accuweather_pollen(location_name, api_key, location_key):
    if not api_key or not location_key:
        return {"Tree": random.randint(2, 7), "Grass": random.randint(3, 8), "Weed": random.randint(1, 5), "Overall": random.randint(3, 8)}
    try:
        # AccuWeather endpoint for 1-day indices. Pollen indices have specific IDs.
        # You'd need to find the IDs for Tree, Grass, Ragweed/Weed, and potentially a composite Air Quality.
        # This example fetches ALL indices, then filters.
        url = f"http://dataservice.accuweather.com/indices/v1/daily/1day/{location_key}?apikey={api_key}&details=true"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        # --- REPLACE THIS DUMMY PARSING WITH ACTUAL ACCUWEATHER RESPONSE PARSING ---
        pollen_levels = {"Tree": 0, "Grass": 0, "Weed": 0, "Overall": 0}
        for item in data:
            # AccuWeather returns 'Value' (raw) and 'CategoryValue' (1-10 index)
            # We'll use 'CategoryValue' if available, otherwise 'Value' mapped
            # Check AccuWeather docs for mapping raw values to 0-10 index
            value = item.get("CategoryValue", item.get("Value", 0)) # Prefer category value
            
            if item.get("Name") == "Tree Pollen":
                pollen_levels["Tree"] = value
            elif item.get("Name") == "Grass Pollen":
                pollen_levels["Grass"] = value
            elif item.get("Name") in ["Ragweed Pollen", "Weed Pollen"]: # AccuWeather might use "Ragweed"
                pollen_levels["Weed"] = value
            # AccuWeather also has an Air Quality Index often, which can be used for "Overall"
            elif item.get("Name") == "Air Quality Index":
                pollen_levels["Overall"] = value
        
        # If overall wasn't found, try to calculate it
        if pollen_levels["Overall"] == 0:
            avg_pollen = sum([pollen_levels[pt] for pt in ["Tree", "Grass", "Weed"] if pollen_levels[pt] is not None]) / 3 if any(pollen_levels[pt] is not None for pt in ["Tree", "Grass", "Weed"]) else 0
            pollen_levels["Overall"] = round(avg_pollen)

        return {k: min(10, v) for k,v in pollen_levels.items()} # Cap at 10

    except Exception as e:
        st.error(f"AccuWeather error for {location_name}: {e}")
        return None

@st.cache_data(ttl=3600)
def fetch_google_pollen(location_name, lat, lon, api_key):
    if not api_key:
        return {"Tree": random.randint(4, 9), "Grass": random.randint(3, 7), "Weed": random.randint(2, 6), "Overall": random.randint(5, 10)}
    try:
        url = f"https://climate.googleapis.com/v1/pollen:lookup?location.latitude={lat}&location.longitude={lon}&days=1&key={api_key}"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        # --- REPLACE THIS DUMMY PARSING WITH ACTUAL GOOGLE POLLEN API RESPONSE PARSING ---
        pollen_levels = {"Tree": 0, "Grass": 0, "Weed": 0, "Overall": 0}
        if data and data.get("dailyInfo"):
            # Get today's info (first entry in dailyInfo)
            today_info = data["dailyInfo"][0]
            
            # Google Pollen API returns 'indexInfo' with 'value' and 'category' for each pollen type
            for pollen_type_info in today_info.get("pollenTypeInfo", []):
                p_type = pollen_type_info.get("code") # e.g., "GRASS", "TREE", "WEED"
                index_val = pollen_type_info.get("indexInfo", {}).get("value")
                
                if p_type == "TREE":
                    pollen_levels["Tree"] = index_val
                elif p_type == "GRASS":
                    pollen_levels["Grass"] = index_val
                elif p_type == "WEED":
                    pollen_levels["Weed"] = index_val
            
            # Google Pollen might not provide a direct "Overall" index, so we calculate
            avg_pollen = sum([pollen_levels[pt] for pt in ["Tree", "Grass", "Weed"] if pollen_levels[pt] is not None]) / 3 if any(pollen_levels[pt] is not None for pt in ["Tree", "Grass", "Weed"]) else 0
            pollen_levels["Overall"] = round(avg_pollen)
            
            return {k: min(10, v) for k,v in pollen_levels.items()} # Cap at 10
        return None
    except Exception as e:
        st.error(f"Google Pollen error for {location_name}: {e}")
        return None

# Placeholder for historical data (remains simulated as per previous suggestion)
@st.cache_data(ttl=3600)
def get_historical_pollen_data(location_name):
    data = []
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    # Simulate historical data for a generic "overall" pollen index
    # For a real project, this would come from your persistent storage.
    for i in range(31):
        date = start_date + timedelta(days=i)
        for provider_name in ["Ambee", "OpenMeteo", "AccuWeather", "Google Pollen"]:
            # Simple random walk for more realistic trend
            current_level = random.uniform(2, 8) + (i / 10) * (random.choice([-1, 1]))
            current_level = max(0, min(10, current_level)) # Keep between 0-10
            data.append({
                "Date": date,
                "Location": location_name,
                "Provider": provider_name,
                "PollenType": "Overall",
                "Level": current_level
            })
    df = pd.DataFrame(data)
    df['Date'] = pd.to_datetime(df['Date'])
    return df

# --- Streamlit App Layout ---
st.set_page_config(
    page_title="Pollen Dashboard",
    page_icon="🌸",
    layout="wide",
    initial_sidebar_state="expanded"
)

st.title(f"🌸 Pollen Dashboard")
st.write(f"Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
st.markdown("---")

st.sidebar.header("Select Location")
selected_location_name = st.sidebar.selectbox("Choose a city:", list(LOCATIONS.keys()))
selected_location = LOCATIONS[selected_location_name]

st.header(f"Current Pollen Levels for {selected_location_name}")

# Container for current data cards
current_data_cols = st.columns(4) # One column per provider

current_pollen_summary = []

providers_map = {
    "Ambee": (fetch_ambee_pollen, AMBEE_API_KEY),
    "OpenMeteo": (fetch_openmeteo_pollen, None), # No key needed for non-commercial
    "AccuWeather": (fetch_accuweather_pollen, ACCUWEATHER_API_KEY, selected_location["accuweather_key"]),
    "Google Pollen": (fetch_google_pollen, GOOGLE_POLLEN_API_KEY),
}

with st.spinner(f"Fetching current pollen for {selected_location_name}..."):
    for i, (provider_name, provider_args) in enumerate(providers_map.items()):
        fetch_func = provider_args[0]
        api_key = provider_args[1] if len(provider_args) > 1 else None
        
        # Handle specific arguments for each API function
        if provider_name == "AccuWeather":
            accuweather_location_key = provider_args[2]
            pollen_levels = fetch_func(selected_location_name, api_key, accuweather_location_key)
        elif provider_name == "OpenMeteo":
            pollen_levels = fetch_func(selected_location_name, selected_location["lat"], selected_location["lon"])
        elif provider_name == "Ambee" or provider_name == "Google Pollen":
            pollen_levels = fetch_func(selected_location_name, selected_location["lat"], selected_location["lon"], api_key)
        else:
            pollen_levels = None # Should not happen with defined map

        with current_data_cols[i]:
            st.subheader(provider_name)
            if pollen_levels:
                df_current = pd.DataFrame([pollen_levels]).T.reset_index()
                df_current.columns = ["PollenType", "Level"]
                
                # Plotting a bar chart for current levels of different types
                fig = px.bar(df_current, x="PollenType", y="Level",
                            color="PollenType",
                            height=250,
                            range_y=[0, 10],
                            labels={"Level": "Index"})
                fig.update_layout(showlegend=False, margin=dict(l=20, r=20, t=50, b=20),
                                  xaxis_title="", yaxis_title="Pollen Index")
                st.plotly_chart(fig, use_container_width=True, config={'displayModeBar': False})
                
                # Add overall to summary table
                if "Overall" in pollen_levels:
                    current_pollen_summary.append({
                        "Location": selected_location_name,
                        "Provider": provider_name,
                        "Overall Index": pollen_levels["Overall"]
                    })
            else:
                st.warning("Data not available.")

st.markdown("---")
st.header("Overall Pollen Index Comparison (Current)")
if current_pollen_summary:
    df_overall_summary = pd.DataFrame(current_pollen_summary)
    fig_overall_compare = px.bar(df_overall_summary, x="Provider", y="Overall Index",
                                title=f"Overall Pollen Index in {selected_location_name}",
                                color="Provider",
                                range_y=[0, 10],
                                labels={"Overall Index": "Index"})
    fig_overall_compare.update_layout(xaxis_title="", yaxis_title="Overall Index")
    st.plotly_chart(fig_overall_compare, use_container_width=True)
else:
    st.info("No current overall pollen data to compare.")

st.markdown("---")
st.header("Historical Pollen Trends (Simulated Data)")

historical_df = get_historical_pollen_data(selected_location_name)

if not historical_df.empty:
    fig_historical = px.line(historical_df, x="Date", y="Level", color="Provider",
                             title=f"Simulated Historical Overall Pollen for {selected_location_name}",
                             labels={"Level": "Pollen Index"},
                             range_y=[0, 10])
    fig_historical.update_xaxes(title_text="Date")
    fig_historical.update_yaxes(title_text="Pollen Index")
    st.plotly_chart(fig_historical, use_container_width=True)
else:
    st.warning("Historical data not available (simulated).")

st.info("Remember to fill in your actual API keys in `.streamlit/secrets.toml` (locally) and Streamlit Cloud secrets, and adapt API parsing!")