[project]
name = "pollen"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "streamlit (>=1.45.1,<2.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "pandas (>=2.3.0,<3.0.0)",
    "plotly (>=6.1.2,<7.0.0)",
    "pydantic (>=2.11.5,<3.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
