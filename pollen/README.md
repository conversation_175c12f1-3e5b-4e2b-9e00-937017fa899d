# Pollen Data Providers

This project provides asynchronous Python classes for fetching pollen data from multiple providers. Each provider returns data in a standardized `Pollen` model format.

## Providers

### 1. OpenMeteoProvider
- **API Key Required**: No (free for non-commercial use)
- **Coverage**: Europe only
- **Data**: Grass, tree (alder, birch, olive), and weed (ragweed, mugwort) pollen
- **Units**: grains/m³

### 2. AmbeeProvider  
- **API Key Required**: Yes
- **Coverage**: Global
- **Data**: Tree, grass, and weed pollen with subspecies details
- **Units**: Varies by API response

### 3. AccuWeatherProvider
- **API Key Required**: Yes  
- **Coverage**: Global
- **Data**: Tree, grass, and weed pollen indices
- **Units**: Index values (typically 0-10)

## Installation

```bash
# Install dependencies
poetry install

# Or with pip
pip install aiohttp pydantic
```

## Usage

### Basic Example

```python
import asyncio
from openmeteo import OpenMeteoProvider
from ambee import AmbeeProvider
from accuweather import AccuWeatherProvider

async def get_pollen_data():
    lat, lon = 52.5200, 13.4050  # Berlin coordinates
    
    # OpenMeteo (no API key needed)
    openmeteo = OpenMeteoProvider()
    pollen_data = await openmeteo.fetch_pollen(lat, lon)
    
    if pollen_data:
        print(f"Grass pollen: {pollen_data.grass.count if pollen_data.grass else 0}")
        print(f"Tree pollen: {pollen_data.tree.count if pollen_data.tree else 0}")
        print(f"Weed pollen: {pollen_data.weed.count if pollen_data.weed else 0}")
    
    # Ambee (requires API key)
    ambee = AmbeeProvider("your-ambee-api-key")
    pollen_data = await ambee.fetch_pollen(lat, lon)
    
    # AccuWeather (requires API key)
    accuweather = AccuWeatherProvider("your-accuweather-api-key")
    pollen_data = await accuweather.fetch_pollen(lat, lon)

asyncio.run(get_pollen_data())
```

### Environment Variables

Set API keys as environment variables:

```bash
export AMBEE_API_KEY="your-ambee-api-key"
export ACCUWEATHER_API_KEY="your-accuweather-api-key"
```

## Data Model

All providers return a `Pollen` object with the following structure:

```python
class Pollen(BaseModel):
    id: UUID                    # Unique identifier
    timestamp: datetime         # When data was retrieved
    coordinates: CoordinatesModel  # Lat/lon coordinates
    tree: PollenSpecies | None     # Tree pollen data
    grass: PollenSpecies | None    # Grass pollen data  
    weed: PollenSpecies | None     # Weed pollen data

class PollenSpecies(BaseModel):
    count: int                     # Total count
    subspecies: List[PollenSubspecies] | None  # Detailed breakdown

class PollenSubspecies(BaseModel):
    name: str                      # Species name (e.g., "birch", "ragweed")
    count: int                     # Species-specific count
```

## Testing

Run the test script to verify providers work:

```bash
python test_providers.py
```

Run the example usage:

```bash
python example_usage.py
```

## API Key Setup

### Ambee
1. Sign up at [Ambee](https://www.getambee.com/)
2. Get your API key from the dashboard
3. Set `AMBEE_API_KEY` environment variable

### AccuWeather
1. Sign up at [AccuWeather Developer Portal](https://developer.accuweather.com/)
2. Create an app to get your API key
3. Set `ACCUWEATHER_API_KEY` environment variable

## Notes

- OpenMeteo pollen data is only available for European locations
- Pollen data availability varies by season and location
- All methods are asynchronous and return `None` if data is unavailable
- Error handling is built-in - failed requests return `None` rather than raising exceptions
